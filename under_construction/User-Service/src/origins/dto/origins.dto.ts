import { IsEnum, <PERSON>NotEmpty, <PERSON>UUID, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Regexs } from '../../utils/constants';

export enum OriginType {
  // APP = 'app',
  BUNDLER = 'bundler',
  PAYMASTER = 'paymaster',
  RELAYER = 'relayer',
}

export class CreateOriginDto {
  @ApiProperty({
    description: 'Origin for the AppConfig',
    example: 'https://example.com',
  })
  @IsNotEmpty()
  @Matches(Regexs.Url, {
    message: 'Invalid URL format (ex: https://example.com)',
  })
  originUrl: string;

  @ApiProperty({
    description: 'Transaction type for your application',
    enum: [OriginType.BUNDLER, OriginType.PAYMASTER],
  })
  @IsNotEmpty()
  @IsEnum(OriginType)
  type: string;

  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;
}

export class OriginTypeDto {
  @ApiProperty({
    description: 'origin configuration type',
    enum: Object.values(OriginType),
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(OriginType, { message: 'Invalid configuration type' })
  type: OriginType;

  @ApiProperty({
    description: 'Id of app',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;
}
export class InputOriginDto extends OriginTypeDto {
  @ApiProperty({
    description: 'Origin for the AppConfig',
    example: 'https://example.com',
  })
  @IsNotEmpty()
  @Matches(Regexs.Url, {
    message: 'Invalid URL format (ex: https://example.com)',
  })
  origin: string;

  @ApiProperty({
    description: 'origin configuration type',
    enum: Object.values(OriginType),
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(OriginType, { message: 'Invalid configuration type' })
  type: OriginType;
}

export class RemoveOriginDto {
  @ApiProperty({
    description: 'Id of your origin',
    required: true,
  })
  @IsNotEmpty()
  originId: number;

  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;

  @ApiProperty({
    description: 'origin configuration type',
    enum: Object.values(OriginType),
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(OriginType, { message: 'Invalid configuration type' })
  type: OriginType;
}

export class FetchOriginsDto extends OriginTypeDto {
  @ApiProperty({
    description: 'Page number',
    required: false,
    default: 1,
  })
  page?: number;

  @ApiProperty({
    description: 'Limit per page',
    required: false,
    default: 10,
  })
  limit?: number;
}
